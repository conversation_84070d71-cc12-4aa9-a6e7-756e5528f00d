<script setup>
import { ref } from "vue";
import OcrShow from "./components/OcrShow.vue";
import FinOcrShow from "./components/FinOcrShow.vue";
import FullTextOcrShow from "./components/FullTextOcrShow.vue";
import ZxOcrShow from "./components/ZxOcrShow.vue";

import Index from "./index.vue";

const currentView = ref(null);
const ocrShowRef = ref(null);
const showAlert = ref(true);

const handleTypeSelect = (type) => {
  if (currentView.value && ocrShowRef.value) {
    ocrShowRef.value.reset();
  }
  currentView.value = type;
  showAlert.value = false;
};
</script>

<template>
  <n-message-provider>
    <div class="app-container">
      <Index @select-type="handleTypeSelect" />
      <div class="content-wrapper">
        <div class="ocr-wrapper">
          <div class="ocr-content">
            <OcrShow
              v-if="
                currentView &&
                currentView !== 'finance' &&
                currentView !== 'fulltext' &&
                currentView !== 'credit'
              "
              ref="ocrShowRef"
              :type="currentView"
            />
            <FinOcrShow
              v-if="currentView === 'finance'"
              ref="ocrShowRef"
              :type="currentView"
            />
            <FullTextOcrShow
              v-if="currentView === 'fulltext'"
              ref="ocrShowRef"
              :type="currentView"
            />
            <ZxOcrShow
              v-if="currentView === 'credit'"
              ref="ocrShowRef"
              :type="currentView"
            />
            <div v-else-if="showAlert" class="empty-state">
              <n-alert title="提示" type="warning">
                1、请选择识别的类型,当前是测试环境,比较缓慢，请耐心等待 <br />
                2、请选择识别的图片或者PDF，确保文件是清晰、图片是正(不能歪斜)、文件不要超过2M。
                <br />
                3、每次使用并发限制为1，如恶意使用，将封禁IP。
              </n-alert>
            </div>
          </div>
        </div>
      </div>
    </div>
  </n-message-provider>
</template>

<style scoped>
.app-container {
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  padding: 2rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.content-wrapper {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.ocr-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.ocr-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
